// components/layout/Navigation.tsx

import logo from "@assets/logo.png";
import { AddLeadToggle } from "@components/addLead/addLeadToggle";
import { Input } from "@components/common/input";
import { LanguageToggleButton } from "@components/languageToggleButton";
import { MagnifyingGlassIcon } from "@phosphor-icons/react";
import { useTranslation } from "react-i18next";

export const Header = () => {
  const { t } = useTranslation();

  return (
    <div className="navbar flex justify-between bg-base-content px-6 py-2 shadow-sm">
      <img
        src={logo}
        alt="DinoDesk Logo"
        className="mask mask-circle h-10 w-10"
      />
      <Input
        className="!w-80 !h-11"
        icon={<MagnifyingGlassIcon size={24} weight="bold" />}
        type="search"
        placeholder={t("navigation.nameAndPhone")}
      />
      <div className="flex items-center gap-4">
        <AddLeadToggle />
        <LanguageToggleButton />
      </div>
    </div>
  );
};
