import { cn } from "@utils/cn";

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  className?: string;
  prefixIcon?: React.ReactNode;
  suffixIcon?: React.ReactNode;
}

const BASE_CLASSES = "h-fit text-body-xs placeholder:text-color-neutral";

export const Input = ({
  className,
  prefixIcon,
  suffixIcon,
  ...props
}: InputProps) => {
  return (
    <label
      className={cn(
        "input input-primary !outline-offset-0 h-8 w-full rounded-lg border-none bg-base-200",
        className
      )}
    >
      {prefixIcon}
      <input required {...props} className={cn(BASE_CLASSES, "w-full")} />
      {suffixIcon}
    </label>
  );
};
